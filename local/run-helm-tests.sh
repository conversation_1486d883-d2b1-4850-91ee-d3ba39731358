#!/bin/bash

set -eo pipefail

DEBUG=""
CUSTOM_TESTS=()
ENABLE_CLEANUP=false
ENABLE_NODE_LABELING=false
RUN_ALL_TESTS=false

CHART_NAME="ay-chart"
BASE_NAME="universal-test"
CHART="./src"

ALL_TEST_CASES=(
  "deployment-basic=1"
  "dns-enabled=1"
  "dns-disabled=1"
  "ingress-enabled=1"
  "ingress-disabled=1"
  "autoscaling-enabled=1"
  "autoscaling-disabled=1"
  "pvc-enabled=1"
  "pvc-disabled=1"
  "healthcheck-enabled=1"
  "healthcheck-disabled=1"
  "prometheus-enabled=1"
  "prometheus-disabled=1"
  "configmap-app=1"
  "configmap-debug=1"
  "configmap-logging=1"
  "configmap-disabled=1"
  "configmap-multiple=1"
  "node-affinity=1"
  "statefulset-enabled=1"
  "statefulset-pvc=1"
  "statefulset-healthcheck=1"
  "statefulset-prometheus=1"
  "statefulset-configmap-app=1"
  "statefulset-configmap-debug=1"
  "statefulset-configmap-logging=1"
  "statefulset-configmap-disabled=1"
  "statefulset-configmap-multiple=1"
)

ALL_TEST_KEYS=()
for i in "${ALL_TEST_CASES[@]}"; do
  ALL_TEST_KEYS+=("${i%=*}")
done

log() { echo -e "\033[1;36m[TEST]\033[0m $*"; }
fail() { echo -e "\033[1;31m[FAIL]\033[0m $*"; exit 1; }

print_help() {
  echo -e "\nUsage: $0 [options]"
  echo -e "\nOptions:"
  echo "  -test all                    Run all test cases"
  echo "  -test <name1,name2,...>      Run specific test cases (comma-separated)"
  echo "  -label                       Enable node labeling (required for node affinity tests)"
  echo "  -cleanup                     Enable cleanup of test namespaces after completion"
  echo "  -debug                       Enable Helm debug output"
  echo "  -help                        Show this help menu"
  echo -e "\nExamples:"
  echo "  $0 -test all -label -cleanup                    # Run all tests with node labeling and cleanup"
  echo "  $0 -test deployment-basic,dns-enabled           # Run specific tests only"
  echo "  $0 -test all -label                             # Run all tests with node labeling, no cleanup"
  echo -e "\nAvailable test names:"
  for t in "${ALL_TEST_KEYS[@]}"; do
    echo "  - $t"
  done
  exit 0
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    -test)
      if [[ -z "$2" ]]; then
        echo -e "\033[1;31m[FAIL]\033[0m -test requires an argument (all or comma-separated test names)"
        exit 1
      fi
      if [[ "$2" == "all" ]]; then
        RUN_ALL_TESTS=true
      else
        IFS=',' read -ra TEST_NAMES <<< "$2"
        for test_name in "${TEST_NAMES[@]}"; do
          if [[ ! " ${ALL_TEST_KEYS[*]} " =~ " $test_name " ]]; then
            echo -e "\033[1;31m[FAIL]\033[0m Unknown test case: $test_name. Use '-help' to list available tests."
            exit 1
          fi
          CUSTOM_TESTS+=("$test_name")
        done
      fi
      shift 2
      ;;
    -label)
      ENABLE_NODE_LABELING=true
      shift
      ;;
    -cleanup)
      ENABLE_CLEANUP=true
      shift
      ;;
    -debug)
      DEBUG="debug"
      shift
      ;;
    -help|--help|-h)
      print_help
      ;;
    *)
      echo -e "\033[1;31m[FAIL]\033[0m Unknown option: $1. Use '-help' for usage information."
      exit 1
      ;;
  esac
done

# Validate that at least one test option is provided
if [[ "$RUN_ALL_TESTS" == "false" && ${#CUSTOM_TESTS[@]} -eq 0 ]]; then
  echo -e "\033[1;31m[FAIL]\033[0m No tests specified. Use '-test all' to run all tests or '-test <names>' for specific tests."
  echo "Use '-help' for usage information."
  exit 1
fi

log "Debug: RUN_ALL_TESTS=$RUN_ALL_TESTS, CUSTOM_TESTS array has ${#CUSTOM_TESTS[@]} elements: ${CUSTOM_TESTS[*]}"

TEST_CASES=()
if [[ "$RUN_ALL_TESTS" == "true" ]]; then
  TEST_CASES=("${ALL_TEST_CASES[@]}")
  log "Running all ${#TEST_CASES[@]} test cases"
else
  for t in "${CUSTOM_TESTS[@]}"; do
    for i in "${ALL_TEST_CASES[@]}"; do
      [[ "${i%=*}" == "$t" ]] && TEST_CASES+=("$i") && break
    done
  done
  log "Running ${#TEST_CASES[@]} specific test cases: ${CUSTOM_TESTS[*]}"
fi

log "Debug: Final TEST_CASES array has ${#TEST_CASES[@]} elements"

log "Waiting for cluster to be ready..."
for ((i = 1; i <= 60; i++)); do
  if kubectl get nodes >/dev/null 2>&1; then
    log "Cluster is ready."
    break
  fi
  sleep 2
done

if ! kubectl get nodes >/dev/null 2>&1; then
  fail "Cluster did not become ready in time."
fi

if [[ "$ENABLE_NODE_LABELING" == "true" ]]; then
  log "Labeling nodes..."
  kubectl get nodes -o name | while read -r NODE; do
    kubectl label "$NODE" \
      assertive.cc/block-node=true \
      node.kubernetes.io/instance-type=standard \
      node.cluster.x-k8s.io/node-pool=bm \
      kubernetes.io/os=linux \
      kubernetes.io/arch=amd64 \
      --overwrite >/dev/null 2>&1 || true
  done
else
  log "Skipping node labeling (use '-label' flag to enable)"
fi

export REGISTRY_SERVER="${REGISTRY_SERVER:-}"
export REGISTRY_USERNAME="${REGISTRY_USERNAME:-}"
export REGISTRY_PASSWORD="${REGISTRY_PASSWORD:-}"
export REGISTRY_EMAIL="${REGISTRY_EMAIL:-}"

# Patch values.yaml with registry credentials
yq eval -i ".deployment.containerSettings.imagePullSecrets.registry.server = strenv(REGISTRY_SERVER)" ${CHART}/values.yaml
yq eval -i ".deployment.containerSettings.imagePullSecrets.registry.username = strenv(REGISTRY_USERNAME)" ${CHART}/values.yaml
yq eval -i ".deployment.containerSettings.imagePullSecrets.registry.password = strenv(REGISTRY_PASSWORD)" ${CHART}/values.yaml
yq eval -i ".deployment.containerSettings.imagePullSecrets.registry.email = strenv(REGISTRY_EMAIL)" ${CHART}/values.yaml

declare -A TEST_RESULTS
log "TEST_RESULTS associative array declared"

cleanup_case() {
  local NAME=$1
  local NS="${NAME}-ns"
  echo "Cleaning up $NAME (namespace: $NS)..."
  helm uninstall "$NAME" -n "$NS" >/dev/null 2>&1 || true
  kubectl delete ns "$NS" --ignore-not-found --wait=true || true
}

check_existing_namespaces() {
  for CASE in "${TEST_CASES[@]}"; do
    NS="${BASE_NAME}-${CASE%=*}-ns"
    if kubectl get ns "$NS" >/dev/null 2>&1; then
      fail "Namespace $NS already exists. Please run cleanup manually first."
    fi
  done
}

test_case() {
  local NAME=$1
  local SET_ARGS=$2
  local NS="${NAME}-ns"

  log "Running case: $NAME"

  # Create namespace with error handling
  if ! kubectl create ns "$NS" >/dev/null 2>&1; then
    log "Warning: Failed to create namespace $NS (may already exist)"
  fi

  # Build Helm command with conditional debug flag
  local HELM_CMD="helm upgrade --install $NAME $CHART -n $NS --wait --timeout 120s --set nameOverride=$CHART_NAME --set fullnameOverride=$NAME --set $SET_ARGS"
  if [[ "$DEBUG" == "debug" ]]; then
    HELM_CMD="$HELM_CMD --debug"
  fi

  log "Executing: $HELM_CMD"

  if OUTPUT=$($HELM_CMD 2>&1); then
    log "Helm command succeeded, setting test result..."
    TEST_RESULTS[$NAME]="PASS"
    log "Test result set, logging success..."
    log "✅ $NAME PASSED"
    log "About to check debug flag: DEBUG='$DEBUG'"
    if [[ "${DEBUG:-}" == "debug" ]]; then
      echo "$OUTPUT"
    fi
    log "Debug check completed"
  else
    log "Helm command failed, setting test result..."
    TEST_RESULTS[$NAME]="FAIL"
    log "❌ $NAME FAILED"
    echo -e "\033[1;31m[ERROR]\033[0m Helm failed for $NAME"
    echo "$OUTPUT"
  fi
  log "Exiting test_case function for $NAME"
}

validate() {
  local NAME=$1
  local NS="${NAME}-ns"
  log "Validating resources in namespace: $NS"

  if ! kubectl get ns "$NS" >/dev/null 2>&1; then
    log "Warning: Namespace $NS does not exist, skipping validation"
    return 0
  fi

  RESOURCES=$(kubectl get all -n "$NS" 2>/dev/null | grep -v 'No resources found' || true)
  if [[ -n "$RESOURCES" ]]; then
    echo "$RESOURCES"
  else
    log "No resources found in namespace $NS"
  fi
}

run_all_tests() {
  log "Starting test execution loop for ${#TEST_CASES[@]} test cases..."

  local test_counter=0
  for CASE in "${TEST_CASES[@]}"; do
    test_counter=$((test_counter + 1))
    log "Processing test case $test_counter/${#TEST_CASES[@]}: $CASE"

    case $CASE in
      deployment-basic=1)
        test_case "$BASE_NAME-deploy-basic" "deployment.enabled=true,statefulset.enabled=false"
        log "test_case function returned for deployment-basic"
        ;;
      dns-enabled=1)
        test_case "$BASE_NAME-dns-on" "deployment.enabled=true,statefulset.enabled=false,podSettings.dns.enabled=true"
        log "test_case function returned for dns-enabled"
        ;;
      dns-disabled=1)
        test_case "$BASE_NAME-dns-off" "deployment.enabled=true,statefulset.enabled=false,podSettings.dns.enabled=false"
        log "test_case function returned for dns-disabled"
        ;;
      ingress-enabled=1)
        test_case "$BASE_NAME-ingress-on" "deployment.enabled=true,statefulset.enabled=false,deployment.ingress.enabled=true"
        log "test_case function returned for ingress-enabled"
        ;;
      ingress-disabled=1)
        test_case "$BASE_NAME-ingress-off" "deployment.enabled=true,statefulset.enabled=false,deployment.ingress.enabled=false"
        log "test_case function returned for ingress-disabled"
        ;;
      autoscaling-enabled=1)
        test_case "$BASE_NAME-hpa-on" "deployment.enabled=true,statefulset.enabled=false,deployment.autoscaling.enabled=true"
        log "test_case function returned for autoscaling-enabled"
        ;;
      autoscaling-disabled=1)
        test_case "$BASE_NAME-hpa-off" "deployment.enabled=true,statefulset.enabled=false,deployment.autoscaling.enabled=false"
        log "test_case function returned for autoscaling-disabled"
        ;;
      pvc-enabled=1)
        test_case "$BASE_NAME-pvc-on" "deployment.enabled=true,statefulset.enabled=false,deployment.containerSettings.persistence.enabled=true"
        log "test_case function returned for pvc-enabled"
        ;;
      pvc-disabled=1)
        test_case "$BASE_NAME-pvc-off" "deployment.enabled=true,statefulset.enabled=false,deployment.containerSettings.persistence.enabled=false"
        log "test_case function returned for pvc-disabled"
        ;;
      healthcheck-enabled=1)
        test_case "$BASE_NAME-health-on" "deployment.enabled=true,statefulset.enabled=false,deployment.containerSettings.healthcheck.enabled=true"
        log "test_case function returned for healthcheck-enabled"
        ;;
      healthcheck-disabled=1)
        test_case "$BASE_NAME-health-off" "deployment.enabled=true,statefulset.enabled=false,deployment.containerSettings.healthcheck.enabled=false"
        log "test_case function returned for healthcheck-disabled"
        ;;
      prometheus-enabled=1)
        test_case "$BASE_NAME-prometheus-on" "deployment.enabled=true,statefulset.enabled=false,deployment.containerSettings.prometheusMonitoring.enabled=true"
        log "test_case function returned for prometheus-enabled"
        ;;
      prometheus-disabled=1)
        test_case "$BASE_NAME-prometheus-off" "deployment.enabled=true,statefulset.enabled=false,deployment.containerSettings.prometheusMonitoring.enabled=false"
        log "test_case function returned for prometheus-disabled"
        ;;
      configmap-app=1)
        test_case "$BASE_NAME-cfg-app" "deployment.enabled=true,statefulset.enabled=false,deployment.containerSettings.configMaps[0].enabled=true,deployment.containerSettings.configMaps[0].name=app-config,deployment.containerSettings.configMaps[0].key=config.yaml,deployment.containerSettings.configMaps[0].mountPath=/app/config.yaml"
        log "test_case function returned for configmap-app"
        ;;
      configmap-debug=1)
        test_case "$BASE_NAME-cfg-debug" "deployment.enabled=true,statefulset.enabled=false,deployment.containerSettings.configMaps[1].enabled=true,deployment.containerSettings.configMaps[1].name=debug-config,deployment.containerSettings.configMaps[1].key=debug.yaml,deployment.containerSettings.configMaps[1].mountPath=/app/debug.yaml"
        log "test_case function returned for configmap-debug"
        ;;
      configmap-logging=1)
        test_case "$BASE_NAME-cfg-logging" "deployment.enabled=true,statefulset.enabled=false,deployment.containerSettings.configMaps[2].enabled=true,deployment.containerSettings.configMaps[2].name=logging-config,deployment.containerSettings.configMaps[2].key=logging.yaml,deployment.containerSettings.configMaps[2].mountPath=/app/logging.yaml"
        log "test_case function returned for configmap-logging"
        ;;
      configmap-disabled=1)
        test_case "$BASE_NAME-cfg-disabled" "deployment.enabled=true,statefulset.enabled=false"
        log "test_case function returned for configmap-disabled"
        ;;
      configmap-multiple=1)
        test_case "$BASE_NAME-cfg-multiple" "deployment.enabled=true,statefulset.enabled=false,deployment.containerSettings.configMaps[0].enabled=true,deployment.containerSettings.configMaps[0].name=app-config,deployment.containerSettings.configMaps[0].key=config.yaml,deployment.containerSettings.configMaps[0].mountPath=/app/config.yaml,deployment.containerSettings.configMaps[1].enabled=true,deployment.containerSettings.configMaps[1].name=debug-config,deployment.containerSettings.configMaps[1].key=debug.yaml,deployment.containerSettings.configMaps[1].mountPath=/app/debug.yaml,deployment.containerSettings.configMaps[2].enabled=true,deployment.containerSettings.configMaps[2].name=logging-config,deployment.containerSettings.configMaps[2].key=logging.yaml,deployment.containerSettings.configMaps[2].mountPath=/app/logging.yaml"
        log "test_case function returned for configmap-multiple"
        ;;
      node-affinity=1)
        test_case "$BASE_NAME-node-affinity" "deployment.enabled=true,statefulset.enabled=false,nodeSettings.nodeGroups[0]=bm,nodeSettings.arch[0]=amd64,nodeSettings.os[0]=linux"
        log "test_case function returned for node-affinity"
        ;;
      statefulset-enabled=1)
        test_case "$BASE_NAME-sts-on" "deployment.enabled=false,statefulset.enabled=true"
        log "test_case function returned for statefulset-enabled"
        ;;
      statefulset-pvc=1)
        test_case "$BASE_NAME-sts-pvc" "deployment.enabled=false,statefulset.enabled=true,statefulset.containerSettings.persistence.enabled=true"
        log "test_case function returned for statefulset-pvc"
        ;;
      statefulset-healthcheck=1)
        test_case "$BASE_NAME-sts-health" "deployment.enabled=false,statefulset.enabled=true,statefulset.containerSettings.healthcheck.enabled=true"
        log "test_case function returned for statefulset-healthcheck"
        ;;
      statefulset-prometheus=1)
        test_case "$BASE_NAME-sts-prometheus" "deployment.enabled=false,statefulset.enabled=true,statefulset.containerSettings.prometheusMonitoring.enabled=true"
        log "test_case function returned for statefulset-prometheus"
        ;;
      statefulset-configmap-logging=1)
        test_case "$BASE_NAME-sts-cfg-logging" "deployment.enabled=false,statefulset.enabled=true,statefulset.containerSettings.configMaps[2].enabled=true,statefulset.containerSettings.configMaps[2].name=logging-config,statefulset.containerSettings.configMaps[2].key=logging.yaml,statefulset.containerSettings.configMaps[2].mountPath=/app/logging.yaml"
        log "test_case function returned for statefulset-configmap-logging"
        ;;
      *)
        log "Warning: Unknown test case: $CASE"
        continue
        ;;
    esac

    log "About to validate test case: $CASE"

    # Extract test name for validation
    TEST_NAME="${CASE%=*}"
    VALIDATION_TARGET="$BASE_NAME-$TEST_NAME"
    log "Validation target: $VALIDATION_TARGET"

    # Temporarily disable validation to test if this is causing the issue
    log "Skipping validation for debugging purposes"
    # if validate "$VALIDATION_TARGET"; then
    #   log "Validation completed for $CASE"
    # else
    #   log "Warning: Validation failed for $CASE"
    # fi

    log "Completed test case: $CASE"
    log "---"
    log "About to continue to next test case..."
  done

  log "Exited the test loop - all tests processed"

  log "\n✅ All test cases ran"
  echo -e "\n\033[1;34mTest Summary:\033[0m"
  for name in "${!TEST_RESULTS[@]}"; do
    status=${TEST_RESULTS[$name]}
    if [[ $status == "PASS" ]]; then
      echo -e "  ✔ $name"
    else
      echo -e "  ✘ $name"
    fi
  done
}

cleanup_all() {
  log "Cleaning up test namespaces..."
  for CASE in "${TEST_CASES[@]}"; do
    cleanup_case "$BASE_NAME-${CASE%=*}"
  done
}

if [[ "$ENABLE_CLEANUP" == "true" ]]; then
  log "Cleanup enabled - namespaces will be cleaned up on exit"
  trap cleanup_all EXIT
else
  log "Cleanup disabled - namespaces will remain after tests (use '-cleanup' flag to enable)"
fi

log "About to check existing namespaces for ${#TEST_CASES[@]} test cases..."
check_existing_namespaces
log "Namespace check completed"

log "About to run all tests..."
run_all_tests
log "run_all_tests function completed"

log "Tests completed successfully"
