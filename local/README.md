# Helm E2E Test Suite

This directory contains an end-to-end test suite for the `ay-chart` Helm chart. The test suite validates various configurations and features of the chart in a real Kubernetes environment using Kind (Kubernetes in Docker).

## Overview

The test suite runs comprehensive tests covering:
- **Deployment configurations** (basic deployment, autoscaling, ingress)
- **StatefulSet configurations** (persistence, health checks, monitoring)
- **Pod settings** (DNS, health checks, Prometheus monitoring)
- **Storage** (PVC enabled/disabled scenarios)
- **Configuration management** (ConfigMaps, environment variables)
- **Node affinity** (node selection and placement)

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Registry credentials (if using private images)

### Basic Usage

```bash
# Run all tests with node labeling
docker-compose run helm-e2e-test -test all -label

# Run all tests with cleanup enabled
docker-compose run helm-e2e-test -test all -label -cleanup

# Run specific tests only
docker-compose run helm-e2e-test -test deployment-basic,dns-enabled
```

## Command Line Options

### Required Options
- **`-test all`** - Run all available test cases
- **`-test <names>`** - Run specific test cases (comma-separated)

### Optional Flags
- **`-label`** - Enable node labeling (required for node affinity tests)
- **`-cleanup`** - Enable cleanup of test namespaces after completion
- **`-debug`** - Enable Helm debug output for troubleshooting
- **`-help`** - Show detailed help and available test names

## Available Test Cases

### Deployment Tests
- `deployment-basic` - Basic deployment configuration
- `deployment-dns-enabled` / `deployment-dns-disabled` - Pod DNS settings
- `deployment-ingress-enabled` / `deployment-ingress-disabled` - Ingress configuration
- `deployment-autoscaling-enabled` / `deployment-autoscaling-disabled` - HPA configuration
- `deployment-pvc-enabled` / `deployment-pvc-disabled` - Persistent volume claims
- `deployment-healthcheck-enabled` / `deployment-healthcheck-disabled` - Health check probes
- `deployment-prometheus-enabled` / `deployment-prometheus-disabled` - Prometheus monitoring
- `deployment-configmap-app` - Tests app-config ConfigMap creation and mounting
- `deployment-configmap-debug` - Tests debug-config ConfigMap creation and mounting
- `deployment-configmap-logging` - Tests logging-config ConfigMap creation and mounting
- `deployment-configmap-disabled` - Tests deployment with NO ConfigMaps (negative test)
- `deployment-configmap-multiple` - Tests multiple ConfigMaps enabled simultaneously
- `node-affinity` - Node selection and affinity rules

### StatefulSet Tests
- `statefulset-enabled` - Basic StatefulSet configuration
- `statefulset-dns-enabled` / `statefulset-dns-disabled` - StatefulSet DNS settings
- `statefulset-pvc` - StatefulSet with persistent storage
- `statefulset-healthcheck` - StatefulSet with health checks
- `statefulset-prometheus` - StatefulSet with Prometheus monitoring
- `statefulset-configmap-app` - StatefulSet with app-config ConfigMap
- `statefulset-configmap-debug` - StatefulSet with debug-config ConfigMap
- `statefulset-configmap-logging` - StatefulSet with logging-config ConfigMap
- `statefulset-configmap-disabled` - StatefulSet with NO ConfigMaps (negative test)
- `statefulset-configmap-multiple` - StatefulSet with multiple ConfigMaps

## Test Coverage Parity

The test suite ensures comprehensive coverage for both Deployment and StatefulSet workloads:

### Feature Parity Matrix
| Feature                   | Deployment Tests                 | StatefulSet Tests                                 |
|---------------------------|----------------------------------|---------------------------------------------------|
| **Basic Functionality**   | ✅ `deployment-basic`             | ✅ `statefulset-enabled`                           |
| **DNS Configuration**     | ✅ `dns-enabled/disabled`         | ✅ `statefulset-dns-enabled/disabled`              |
| **Health Checks**         | ✅ `healthcheck-enabled/disabled` | ✅ `statefulset-healthcheck`                       |
| **Prometheus Monitoring** | ✅ `prometheus-enabled/disabled`  | ✅ `statefulset-prometheus`                        |
| **Persistent Storage**    | ✅ `pvc-enabled/disabled`         | ✅ `statefulset-pvc`                               |
| **ConfigMap - App**       | ✅ `configmap-app`                | ✅ `statefulset-configmap-app`                     |
| **ConfigMap - Debug**     | ✅ `configmap-debug`              | ✅ `statefulset-configmap-debug`                   |
| **ConfigMap - Logging**   | ✅ `configmap-logging`            | ✅ `statefulset-configmap-logging`                 |
| **ConfigMap - Disabled**  | ✅ `configmap-disabled`           | ✅ `statefulset-configmap-disabled`                |
| **ConfigMap - Multiple**  | ✅ `configmap-multiple`           | ✅ `statefulset-configmap-multiple`                |
| **Ingress**               | ✅ `ingress-enabled/disabled`     | ❌ N/A (StatefulSets use headless services)        |
| **Autoscaling**           | ✅ `autoscaling-enabled/disabled` | ❌ N/A (StatefulSets don't support HPA by default) |

### Workload-Specific Features
- **Deployments**: Include ingress and autoscaling tests (stateless workload features)
- **StatefulSets**: Focus on persistent storage and stable network identity features
- **Both**: Share common features like DNS, health checks, monitoring, and ConfigMaps

### Comprehensive ConfigMap Testing
Both Deployment and StatefulSet workloads include identical ConfigMap test coverage:
- Individual ConfigMap tests (app, debug, logging)
- Negative testing (disabled ConfigMaps)
- Multiple ConfigMaps scenarios
- Volume mounting validation
- Content verification

## ConfigMap Testing Details

The ConfigMap test suite provides comprehensive validation of configuration management capabilities, covering both positive and negative scenarios.

### ConfigMap Test Cases

#### Individual ConfigMap Tests
- **`configmap-app`** - Tests application configuration
  - Creates `app-config` ConfigMap with application settings
  - Mounts at `/app/config.yaml` with content: `log_level: INFO, retries: 3`
  - Validates volume mount and file accessibility

- **`configmap-debug`** - Tests debug configuration
  - Creates `debug-config` ConfigMap with debug settings
  - Mounts at `/app/debug.yaml` with content: `debug: true, verbose: true`
  - Validates debug-specific configuration mounting

- **`configmap-logging`** - Tests logging configuration
  - Creates `logging-config` ConfigMap with logging settings
  - Mounts at `/app/logging.yaml` with content: `log_format: json, log_rotation: daily`
  - Validates logging-specific configuration mounting

#### Scenario Tests
- **`configmap-disabled`** - Negative test case
  - Deploys application with NO ConfigMaps enabled
  - Validates that no ConfigMap resources are created
  - Confirms no volume mounts are added to pods
  - Ensures deployment succeeds without configuration

- **`configmap-multiple`** - Multiple ConfigMaps test
  - Enables all three ConfigMaps simultaneously
  - Validates all ConfigMaps are created with correct content
  - Confirms all volume mounts are properly configured
  - Tests complex configuration scenarios

#### StatefulSet ConfigMap Tests
- **`statefulset-configmap-app`** - StatefulSet with app configuration
- **`statefulset-configmap-debug`** - StatefulSet with debug configuration
- **`statefulset-configmap-logging`** - StatefulSet with logging configuration
- **`statefulset-configmap-disabled`** - StatefulSet with NO ConfigMaps (negative test)
- **`statefulset-configmap-multiple`** - StatefulSet with multiple ConfigMaps

All StatefulSet ConfigMap tests validate:
- ConfigMap mounting in StatefulSet workloads
- Persistent storage + configuration combination
- StatefulSet-specific volume handling
- Headless service integration with ConfigMaps

### ConfigMap Validation Process

Each ConfigMap test validates:

1. **Resource Creation**
   - ConfigMap resource exists in correct namespace
   - ConfigMap contains expected data keys and content
   - ConfigMap has proper labels and metadata

2. **Volume Configuration**
   - Deployment/StatefulSet has correct volume definitions
   - Volume references correct ConfigMap name
   - Volume mount paths are properly configured

3. **Pod Integration**
   - Pods have volume mounts at specified paths
   - Files are accessible inside containers
   - Content matches expected configuration

4. **Negative Scenarios**
   - Disabled ConfigMaps don't create resources
   - No volume mounts when ConfigMaps are disabled
   - Deployment succeeds without configuration dependencies

### ConfigMap Structure Tested

```yaml
configMaps:
  - enabled: true/false
    name: app-config
    key: config.yaml
    mountPath: /app/config.yaml
    content: |
      log_level: INFO
      retries: 3
  - enabled: true/false
    name: debug-config
    key: debug.yaml
    mountPath: /app/debug.yaml
    content: |
      debug: true
      verbose: true
  - enabled: true/false
    name: logging-config
    key: logging.yaml
    mountPath: /app/logging.yaml
    content: |
      log_format: json
      log_rotation: daily
```

### ConfigMap Testing Examples

```bash
# Test individual ConfigMap scenarios
docker-compose run helm-e2e-test -test configmap-app
docker-compose run helm-e2e-test -test configmap-debug
docker-compose run helm-e2e-test -test configmap-logging

# Test negative scenario (no ConfigMaps)
docker-compose run helm-e2e-test -test configmap-disabled

# Test multiple ConfigMaps together
docker-compose run helm-e2e-test -test configmap-multiple

# Test all ConfigMap scenarios
docker-compose run helm-e2e-test -test configmap-app,configmap-debug,configmap-logging,configmap-disabled,configmap-multiple

# Test StatefulSet with ConfigMaps (individual)
docker-compose run helm-e2e-test -test statefulset-configmap-app
docker-compose run helm-e2e-test -test statefulset-configmap-debug
docker-compose run helm-e2e-test -test statefulset-configmap-logging

# Test StatefulSet ConfigMap scenarios
docker-compose run helm-e2e-test -test statefulset-configmap-disabled
docker-compose run helm-e2e-test -test statefulset-configmap-multiple

# Test all StatefulSet ConfigMap scenarios
docker-compose run helm-e2e-test -test statefulset-configmap-app,statefulset-configmap-debug,statefulset-configmap-logging,statefulset-configmap-disabled,statefulset-configmap-multiple
```

## Usage Examples

### Development Workflow
```bash
# Quick test run (no cleanup for faster iteration)
docker-compose run helm-e2e-test -test deployment-basic,dns-enabled

# Full test suite with node labeling
docker-compose run helm-e2e-test -test all -label

# Production-like test (with cleanup)
docker-compose run helm-e2e-test -test all -label -cleanup
```

### Debugging Failed Tests
```bash
# Run with debug output
docker-compose run helm-e2e-test -test deployment-basic -debug

# Run specific failing test without cleanup to inspect resources
docker-compose run helm-e2e-test -test prometheus-enabled
```

### CI/CD Pipeline
```bash
# Recommended for CI: all tests with cleanup
docker-compose run helm-e2e-test -test all -label -cleanup
```

## Environment Variables

Set these environment variables for private registry access:

```bash
export REGISTRY_USERNAME="your-username"
export REGISTRY_PASSWORD="your-password"
export REGISTRY_EMAIL="<EMAIL>"
```

## Test Architecture

### Infrastructure
- **Kind cluster** - Local Kubernetes cluster in Docker
- **Multi-node setup** - Tests node affinity and scheduling
- **Isolated namespaces** - Each test runs in its own namespace

### Test Flow
1. **Cluster Setup** - Kind cluster creation and node preparation
2. **Node Labeling** (optional) - Apply labels for node affinity tests
3. **Test Execution** - Deploy chart with specific configurations
4. **Validation** - Verify resources are created correctly
5. **Cleanup** (optional) - Remove test namespaces and resources

## Troubleshooting

### Common Issues

**Tests not running:**
```bash
# Ensure you specify which tests to run
docker-compose run helm-e2e-test -test all -label
```

**Node affinity tests failing:**
```bash
# Enable node labeling for node affinity tests
docker-compose run helm-e2e-test -test node-affinity -label
```

**Registry authentication errors:**
```bash
# Set registry credentials
export REGISTRY_USERNAME="your-username"
export REGISTRY_PASSWORD="your-password"
```

### Debugging Tips

1. **Use `-debug` flag** for detailed Helm output
2. **Skip `-cleanup`** to inspect resources after test failure
3. **Run specific tests** to isolate issues
4. **Check Kind cluster status**: `kind get clusters`
5. **Inspect namespaces**: `kubectl get namespaces`

## Files Structure

```
local/
├── README.md              # This documentation
├── run-helm-tests.sh      # Main test script
├── entrypoint.sh          # Docker entrypoint
├── kind-config.yaml       # Kind cluster configuration
└── docker-compose.yml     # Docker Compose setup
```

## Contributing

When adding new tests:
1. Add test case to `ALL_TEST_CASES` array in `run-helm-tests.sh`
2. Add corresponding case in `run_all_tests()` function
3. Update this README with the new test description
4. Test the new case in isolation before adding to CI

## Performance Notes

- **Without cleanup**: Tests run faster, useful for development
- **With cleanup**: Slower but ensures clean state, recommended for CI
- **Specific tests**: Much faster than running all tests
- **Node labeling**: Adds ~10 seconds but required for node affinity tests
