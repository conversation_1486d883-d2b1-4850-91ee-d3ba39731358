fullnameOverride: ""
nameOverride: ""
# Project metadata for labeling and traceability
project:
  team: "sysops" # Required: The team responsible for the chart
  url: "dummy-app-01" # Required: Project Git URL or reference
# Pod-specific settings
podSettings:
  dns:
    enabled: false # Enable custom DNS policy and config
    policy: "ClusterFirst" # DNS policy to apply to pods
    ndots: "2" # DNS ndots setting for Kubernetes resolvers
# Main deployment settings
deployment:
  enabled: true # Enables the Deployment resource
  replicas: 1 # Number of replicas if autoscaling is disabled
  terminationGracePeriodSeconds: 60 # Pod shutdown grace period
  containerPort: 80 # Main container port
  clusterService: true # Create a ClusterIP service
  strategy:
    type: RollingUpdate # Deployment strategy: RollingUpdate or Recreate
    maxUnavailable: 1 # Max unavailable pods during update
    maxSurge: 1 # Max surge pods during update
  autoscaling:
    enabled: false # Enable Horizontal Pod Autoscaler
    minReplicas: 2
    maxReplicas: 10
    targetCPU: 80 # Target CPU utilization (%)
    targetMemory: 80 # Target memory utilization (%)
  ingress:
    enabled: false # Enable ingress resource
    public: false # Mark ingress as public (for internal logic or annotation)
    hosts:
      - host: sample.assertive.cc # Ingress hostname
        paths:
          - / # URL paths to route
    tls: [] # List of TLS secrets for HTTPS
    annotations:
      haproxy.org/backend-config-snippet: |
        http-request set-header X-Forwarded-Proto https  # Example annotation for HAProxy
  containerSettings:
    image:
      repository: "registry.gitlab.com/assertiveyield/infrastructure/playground/dummy-test-01/default" # Docker image
      tag: "latest" # Image tag
      pullPolicy: "IfNotPresent" # Image pull policy: Always, IfNotPresent, Never
    imagePullSecrets:
      enabled: true
      name: regcred
      registry:
        server: registry.gitlab.com
        username: "gitlab+deploy-token-8358786"
        password: "gldt-5ziy7p_Xi-4rTxELF7dr"
        email: ""
    resources:
      limits:
        cpu: 100m
        memory: 128Mi
      requests:
        cpu: 100m
        memory: 128Mi
    env:
      AY_ENVIRONMENT: Production # Custom environment variables
    prometheusMonitoring:
      enabled: false # Enable Prometheus scraping annotations
      path: "/metrics" # Path exposed for metrics
      port: 8080 # Port for Prometheus to scrape
    healthcheck:
      enabled: false # Enable health probes
      livenessProbe:
        livenessCheckIntervalSeconds: 30
        livenessCheckTimeoutSeconds: 10
        livenessFailureThreshold: 3
        livenessInitialDelaySeconds: 10
        livenessUrl: "/"
      readinessProbe:
        readinessCheckIntervalSeconds: 30
        readinessCheckTimeoutSeconds: 10
        readinessFailureThreshold: 10
        readinessInitialDelaySeconds: 10
        readinessUrl: "/"
      startupProbe:
        startupCheckIntervalSeconds: 30
        startupCheckTimeoutSeconds: 10
        startupFailureThreshold: 10
        startupInitialDelaySeconds: 0
        startupUrl: "/"
    persistence:
      enabled: false # Enable persistent storage
      accessModes:
        - ReadWriteOnce
      size: 1Gi
      storageClassName: "" # Leave empty for default
      mountPath: /data # Where to mount inside the container
      subPath: "" # Optional: specify subPath inside PVC
    # Optional configMaps to mount into the container
    configMaps:
      - enabled: false # Enable this ConfigMap
        name: app-config # Name of the ConfigMap resource and volume
        key: config.yaml # Key inside the ConfigMap (used as filename)
        mountPath: /etc/app/config.yaml # Path inside container
        content: |
          log_level: INFO
          retries: 3
      - enabled: false
        name: debug-config
        key: debug.yaml
        mountPath: /etc/app/debug.yaml
        content: |
          debug: true
          verbose: true
      - enabled: false
        name: unused-config
        key: unused.yaml
        mountPath: /etc/app/unused.yaml
        content: |
          should_not_render: true
statefulset:
  enabled: false # Set to true to enable StatefulSet
  replicas: 1
  terminationGracePeriodSeconds: 60
  containerPort: 80
  clusterService: true
  strategy:
    type: RollingUpdate # Valid values: RollingUpdate or OnDelete
  containerSettings:
    image:
      repository: "registry.gitlab.com/assertiveyield/infrastructure/playground/dummy-test-01/default"
      tag: "latest"
      pullPolicy: "IfNotPresent"
    imagePullSecrets:
      enabled: true
      name: regcred
      registry:
        server: registry.gitlab.com
        username: your-username
        password: your-password
        email: <EMAIL>
    resources:
      limits:
        cpu: 100m
        memory: 128Mi
      requests:
        cpu: 100m
        memory: 128Mi
    env:
      AY_ENVIRONMENT: Production
    prometheusMonitoring:
      enabled: false
      path: "/metrics"
      port: 8080
    healthcheck:
      enabled: false
      livenessProbe:
        livenessCheckIntervalSeconds: 30
        livenessCheckTimeoutSeconds: 10
        livenessFailureThreshold: 3
        livenessInitialDelaySeconds: 10
        livenessUrl: "/"
      readinessProbe:
        readinessCheckIntervalSeconds: 30
        readinessCheckTimeoutSeconds: 10
        readinessFailureThreshold: 10
        readinessInitialDelaySeconds: 10
        readinessUrl: "/"
      startupProbe:
        startupCheckIntervalSeconds: 30
        startupCheckTimeoutSeconds: 10
        startupFailureThreshold: 10
        startupInitialDelaySeconds: 0
        startupUrl: "/"
    persistence:
      enabled: false
      accessModes:
        - ReadWriteOnce
      size: 1Gi
      storageClassName: ""
      mountPath: /data
      subPath: ""
    configMaps:
      - enabled: false
        name: app-config
        key: config.yaml
        mountPath: /etc/app/config.yaml
        content: |
          log_level: INFO
          retries: 3
      - enabled: false
        name: debug-config
        key: debug.yaml
        mountPath: /etc/app/debug.yaml
        content: |
          debug: true
          verbose: true
      - enabled: false
        name: unused-config
        key: unused.yaml
        mountPath: /etc/app/unused.yaml
        content: |
          should_not_render: true
# Node scheduling preferences
nodeSettings:
  nodeRestrictions: "none" # Options: none, sameNode, differentNodes
  allowOtherAppsOnNode: true # Prevent other apps from scheduling on the same node
  instanceTypes: [] # Optional list of instance types (e.g., ["Standard_D2s_v3"])
  nodeGroups:
    - bm # Node affinity targeting groups (used for labels)
  arch:
    - amd64 # Architecture targeting for nodes (e.g., amd64, arm64)
  os:
    - linux # OS targeting for nodes (e.g., linux, windows)
