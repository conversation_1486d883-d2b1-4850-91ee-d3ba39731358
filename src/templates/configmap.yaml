{{- if .Values.deployment.enabled }}
{{- range $index, $configMap := .Values.deployment.containerSettings.configMaps }}
{{- if and $configMap $configMap.enabled }}
{{- $defaultValues := index $.Values.deployment.containerSettings.configMaps $index }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $configMap.name | default $defaultValues.name | default (printf "%s-config-%d" $.Release.Name $index) }}
  {{- include "ay-chart.commonMetadata" $ | nindent 2 }}
data:
  {{ $configMap.key | default $defaultValues.key | default "config.yaml" }}: |
    {{- $configMap.content | default $defaultValues.content | default "# Default config content" | toString | nindent 4 }}
{{- end }}
{{- end }}
{{- end }}

{{- if .Values.statefulset.enabled }}
{{- range $index, $configMap := .Values.statefulset.containerSettings.configMaps }}
{{- if and $configMap $configMap.enabled }}
{{- $defaultValues := index $.Values.statefulset.containerSettings.configMaps $index }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $configMap.name | default $defaultValues.name | default (printf "%s-config-%d" $.Release.Name $index) }}
  {{- include "ay-chart.commonMetadata" $ | nindent 2 }}
data:
  {{ $configMap.key | default $defaultValues.key | default "config.yaml" }}: |
    {{- $configMap.content | default $defaultValues.content | default "# Default config content" | toString | nindent 4 }}
{{- end }}
{{- end }}
{{- end }}
