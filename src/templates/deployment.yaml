{{- if .Values.deployment.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "ay-chart.name" . }}
  {{ include "ay-chart.commonMetadata" . | nindent 2 }}
  labels:
  {{ include "ay-chart.labels" . | nindent 4 }}
spec:
  {{- if not .Values.deployment.autoscaling.enabled }}
  replicas: {{ .Values.deployment.replicas }}
  {{- end }}
  revisionHistoryLimit: 2
  strategy:
    type: {{ .Values.deployment.strategy.type }}
    {{- if eq .Values.deployment.strategy.type "RollingUpdate" }}
    rollingUpdate:
      maxSurge: {{ .Values.deployment.strategy.maxSurge }}
      maxUnavailable: {{ .Values.deployment.strategy.maxUnavailable }}
    {{- end }}
  selector:
    matchLabels:
      {{ include "ay-chart.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        {{- if (.Values.deployment.containerSettings.prometheusMonitoring).enabled }}
        prometheus.io/path: {{ .Values.deployment.containerSettings.prometheusMonitoring.path | quote }}
        prometheus.io/port: {{ .Values.deployment.containerSettings.prometheusMonitoring.port | quote }}
        prometheus.io/scrape: "true"
        {{- else }}
        prometheus.io/scrape: "false"
        {{- end }}
      labels:
        {{ include "ay-chart.labels" . | nindent 8 }}
    spec:
      {{ include "ay-chart.podSpecs" . | nindent 6 }}
      terminationGracePeriodSeconds: {{ .Values.deployment.terminationGracePeriodSeconds }}
      {{- if .Values.deployment.containerSettings.imagePullSecrets.enabled }}
      imagePullSecrets:
        - name: {{ .Values.deployment.containerSettings.imagePullSecrets.name }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.deployment.containerSettings.image.repository }}:{{ .Values.deployment.containerSettings.image.tag }}"
          imagePullPolicy: {{ .Values.deployment.containerSettings.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.deployment.containerPort }}
              protocol: TCP
          {{- if .Values.deployment.containerSettings.healthcheck.enabled }}
          livenessProbe:
            httpGet:
              path: {{ .Values.deployment.containerSettings.healthcheck.livenessProbe.livenessUrl }}
              port: {{ .Values.deployment.containerPort }}
            initialDelaySeconds: {{ .Values.deployment.containerSettings.healthcheck.livenessProbe.livenessInitialDelaySeconds | default 60 }}
            periodSeconds: {{ .Values.deployment.containerSettings.healthcheck.livenessProbe.livenessCheckIntervalSeconds | default 30 }}
            timeoutSeconds: {{ .Values.deployment.containerSettings.healthcheck.livenessProbe.livenessCheckTimeoutSeconds | default 10 }}
            failureThreshold: {{ .Values.deployment.containerSettings.healthcheck.livenessProbe.livenessFailureThreshold | default 3 }}

          readinessProbe:
            httpGet:
              path: {{ .Values.deployment.containerSettings.healthcheck.readinessProbe.readinessUrl }}
              port: {{ .Values.deployment.containerPort }}
            initialDelaySeconds: {{ .Values.deployment.containerSettings.healthcheck.readinessProbe.readinessInitialDelaySeconds | default 10 }}
            periodSeconds: {{ .Values.deployment.containerSettings.healthcheck.readinessProbe.readinessCheckIntervalSeconds | default 30 }}
            timeoutSeconds: {{ .Values.deployment.containerSettings.healthcheck.readinessProbe.readinessCheckTimeoutSeconds | default 10 }}
            failureThreshold: {{ .Values.deployment.containerSettings.healthcheck.readinessProbe.readinessFailureThreshold | default 10 }}

          startupProbe:
            httpGet:
              path: {{ .Values.deployment.containerSettings.healthcheck.startupProbe.startupUrl }}
              port: {{ .Values.deployment.containerPort }}
            initialDelaySeconds: {{ .Values.deployment.containerSettings.healthcheck.startupProbe.startupInitialDelaySeconds | default 0 }}
            periodSeconds: {{ .Values.deployment.containerSettings.healthcheck.startupProbe.startupCheckIntervalSeconds | default 5 }}
            timeoutSeconds: {{ .Values.deployment.containerSettings.healthcheck.startupProbe.startupCheckTimeoutSeconds | default 5 }}
            failureThreshold: {{ .Values.deployment.containerSettings.healthcheck.startupProbe.startupFailureThreshold | default 10 }}
          {{- end }}

          {{- $hasVolumeMounts := or .Values.deployment.containerSettings.persistence.enabled (gt (len (index .Values.deployment.containerSettings.configMaps | default list)) 0) }}
          {{- if $hasVolumeMounts }}
          volumeMounts:
            {{- if .Values.deployment.containerSettings.persistence.enabled }}
            - name: app-storage
              mountPath: {{ .Values.deployment.containerSettings.persistence.mountPath }}
              {{- if .Values.deployment.containerSettings.persistence.subPath }}
              subPath: {{ .Values.deployment.containerSettings.persistence.subPath }}
              {{- end }}
            {{- end }}
            {{- range $index, $configMap := .Values.deployment.containerSettings.configMaps }}
            {{- if $configMap.enabled }}
            - name: {{ $configMap.name }}-volume
              mountPath: {{ $configMap.mountPath }}
              subPath: {{ $configMap.key }}
            {{- end }}
            {{- end }}
          {{- end }}

      {{- if .Values.deployment.containerSettings.persistence.enabled }}
      volumes:
        - name: app-storage
          persistentVolumeClaim:
            claimName: {{ .Release.Name }}-storage
      {{- end }}
{{- end }}
