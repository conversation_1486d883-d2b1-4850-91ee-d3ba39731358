{{- if .Values.statefulset.enabled }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ template "ay-chart.name" . }}
  {{ include "ay-chart.commonMetadata" . | nindent 2 }}
  labels:
  {{ include "ay-chart.labels" . | nindent 4 }}
spec:
  serviceName: {{ template "ay-chart.name" . }}
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      {{ include "ay-chart.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        {{- if (.Values.statefulset.containerSettings.prometheusMonitoring).enabled }}
        prometheus.io/path: {{ .Values.statefulset.containerSettings.prometheusMonitoring.path | quote }}
        prometheus.io/port: {{ .Values.statefulset.containerSettings.prometheusMonitoring.port | quote }}
        prometheus.io/scrape: "true"
        {{- else }}
        prometheus.io/scrape: "false"
        {{- end }}
      labels:
        {{ include "ay-chart.labels" . | nindent 8 }}
    spec:
      {{ include "ay-chart.podSpecs" . | nindent 6 }}
      terminationGracePeriodSeconds: {{ .Values.statefulset.terminationGracePeriodSeconds }}
      {{- if .Values.statefulset.containerSettings.imagePullSecrets.enabled }}
      imagePullSecrets:
        - name: {{ .Values.statefulset.containerSettings.imagePullSecrets.name }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.statefulset.containerSettings.image.repository }}:{{ .Values.statefulset.containerSettings.image.tag }}"
          imagePullPolicy: {{ .Values.statefulset.containerSettings.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.statefulset.containerPort }}
              protocol: TCP
          {{- if .Values.statefulset.containerSettings.healthcheck.enabled }}
          livenessProbe:
            httpGet:
              path: {{ .Values.statefulset.containerSettings.healthcheck.livenessProbe.livenessUrl }}
              port: {{ .Values.statefulset.containerPort }}
            initialDelaySeconds: {{ .Values.statefulset.containerSettings.healthcheck.livenessProbe.livenessInitialDelaySeconds | default 60 }}
            periodSeconds: {{ .Values.statefulset.containerSettings.healthcheck.livenessProbe.livenessCheckIntervalSeconds | default 30 }}
            timeoutSeconds: {{ .Values.statefulset.containerSettings.healthcheck.livenessProbe.livenessCheckTimeoutSeconds | default 10 }}
            failureThreshold: {{ .Values.statefulset.containerSettings.healthcheck.livenessProbe.livenessFailureThreshold | default 3 }}

          readinessProbe:
            httpGet:
              path: {{ .Values.statefulset.containerSettings.healthcheck.readinessProbe.readinessUrl }}
              port: {{ .Values.statefulset.containerPort }}
            initialDelaySeconds: {{ .Values.statefulset.containerSettings.healthcheck.readinessProbe.readinessInitialDelaySeconds | default 10 }}
            periodSeconds: {{ .Values.statefulset.containerSettings.healthcheck.readinessProbe.readinessCheckIntervalSeconds | default 30 }}
            timeoutSeconds: {{ .Values.statefulset.containerSettings.healthcheck.readinessProbe.readinessCheckTimeoutSeconds | default 10 }}
            failureThreshold: {{ .Values.statefulset.containerSettings.healthcheck.readinessProbe.readinessFailureThreshold | default 10 }}

          startupProbe:
            httpGet:
              path: {{ .Values.statefulset.containerSettings.healthcheck.startupProbe.startupUrl }}
              port: {{ .Values.statefulset.containerPort }}
            initialDelaySeconds: {{ .Values.statefulset.containerSettings.healthcheck.startupProbe.startupInitialDelaySeconds | default 0 }}
            periodSeconds: {{ .Values.statefulset.containerSettings.healthcheck.startupProbe.startupCheckIntervalSeconds | default 5 }}
            timeoutSeconds: {{ .Values.statefulset.containerSettings.healthcheck.startupProbe.startupCheckTimeoutSeconds | default 5 }}
            failureThreshold: {{ .Values.statefulset.containerSettings.healthcheck.startupProbe.startupFailureThreshold | default 10 }}
          {{- end }}

          {{- $hasVolumeMounts := or .Values.statefulset.containerSettings.persistence.enabled (gt (len (index .Values.statefulset.containerSettings.configMaps | default list)) 0) }}
          {{- if $hasVolumeMounts }}
          volumeMounts:
            {{- if .Values.statefulset.containerSettings.persistence.enabled }}
            - name: app-storage
              mountPath: {{ .Values.statefulset.containerSettings.persistence.mountPath }}
              {{- if .Values.statefulset.containerSettings.persistence.subPath }}
              subPath: {{ .Values.statefulset.containerSettings.persistence.subPath }}
              {{- end }}
            {{- end }}
            {{- range $index, $configMap := .Values.statefulset.containerSettings.configMaps }}
            {{- if $configMap.enabled }}
            - name: {{ $configMap.name }}-volume
              mountPath: {{ $configMap.mountPath }}
              subPath: {{ $configMap.key }}
            {{- end }}
            {{- end }}
          {{- end }}

  {{- if .Values.statefulset.containerSettings.persistence.enabled }}
  volumeClaimTemplates:
    - metadata:
        name: app-storage
        labels:
          {{- include "ay-chart.labels" . | nindent 10 }}
      spec:
        accessModes:
          {{- toYaml .Values.statefulset.containerSettings.persistence.accessModes | nindent 10 }}
        resources:
          requests:
            storage: {{ .Values.statefulset.containerSettings.persistence.size }}
        {{- if .Values.statefulset.containerSettings.persistence.storageClassName }}
        storageClassName: {{ .Values.statefulset.containerSettings.persistence.storageClassName }}
        {{- end }}
  {{- end }}
{{- end }}
