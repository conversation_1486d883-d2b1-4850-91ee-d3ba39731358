#!/bin/bash

echo "Testing ConfigMap volume mounting fix..."

# Test 1: Check if deployment template renders with ConfigMap enabled
echo "Test 1: Deployment with ConfigMap enabled"
helm template test-configmap ./src \
  --set deployment.enabled=true \
  --set statefulset.enabled=false \
  --set deployment.containerSettings.configMaps[0].enabled=true \
  --show-only templates/deployment.yaml > deployment-test.yaml

if grep -q "volumeMounts:" deployment-test.yaml && grep -q "configMap:" deployment-test.yaml; then
  echo "✅ Deployment template includes ConfigMap volume mounts"
else
  echo "❌ Deployment template missing ConfigMap volume mounts"
fi

# Test 2: Check if StatefulSet template renders with ConfigMap enabled  
echo "Test 2: StatefulSet with ConfigMap enabled"
helm template test-configmap ./src \
  --set deployment.enabled=false \
  --set statefulset.enabled=true \
  --set statefulset.containerSettings.configMaps[0].enabled=true \
  --show-only templates/statefulset.yaml > statefulset-test.yaml

if grep -q "volumeMounts:" statefulset-test.yaml && grep -q "configMap:" statefulset-test.yaml; then
  echo "✅ StatefulSet template includes ConfigMap volume mounts"
else
  echo "❌ StatefulSet template missing ConfigMap volume mounts"
fi

# Test 3: Check ConfigMap creation
echo "Test 3: ConfigMap creation"
helm template test-configmap ./src \
  --set deployment.enabled=true \
  --set deployment.containerSettings.configMaps[0].enabled=true \
  --show-only templates/configmap.yaml > configmap-test.yaml

if grep -q "kind: ConfigMap" configmap-test.yaml; then
  echo "✅ ConfigMap template creates ConfigMap resources"
else
  echo "❌ ConfigMap template not creating resources"
fi

echo "Generated files for inspection:"
echo "- deployment-test.yaml"
echo "- statefulset-test.yaml" 
echo "- configmap-test.yaml"

echo "Validation complete!"
