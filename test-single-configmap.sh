#!/bin/bash

echo "Testing single ConfigMap fix..."

# Test the app ConfigMap case
echo "Testing app ConfigMap..."
helm template test-configmap ./src \
  --set deployment.enabled=true \
  --set statefulset.enabled=false \
  --set deployment.containerSettings.configMaps[0].enabled=true \
  --set deployment.containerSettings.configMaps[0].name=app-config \
  --set deployment.containerSettings.configMaps[0].key=config.yaml \
  --set deployment.containerSettings.configMaps[0].mountPath=/app/config.yaml \
  --show-only templates/deployment.yaml

echo "---"
echo "Testing ConfigMap creation..."
helm template test-configmap ./src \
  --set deployment.enabled=true \
  --set deployment.containerSettings.configMaps[0].enabled=true \
  --set deployment.containerSettings.configMaps[0].name=app-config \
  --set deployment.containerSettings.configMaps[0].key=config.yaml \
  --set deployment.containerSettings.configMaps[0].mountPath=/app/config.yaml \
  --show-only templates/configmap.yaml
