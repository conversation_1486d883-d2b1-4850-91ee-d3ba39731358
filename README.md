# ay-chart - Universal Helm Chart

`ay-chart` is a reusable Helm chart designed to standardize the deployment of stateless services across Kubernetes clusters used by AssertiveYield.

This chart includes support for deployment strategies, ingress, autoscaling, configMaps, monitoring, DNS overrides, and node scheduling preferences.

## Features

* Deploy stateless services using Kubernetes Deployments
* Optional ingress with TLS and annotations
* Horizontal Pod Autoscaler support
* Health checks: liveness, readiness, and startup probes
* Configurable resource limits and environment variables
* Optional Prometheus monitoring annotations
* Support for multiple ConfigMaps with mount paths
* Node affinity, anti-affinity, and restrictions
* Configurable DNS settings
* Custom labels for traceability

## Usage

### Add the chart

To install the chart:

```bash
helm install my-app ./ay-chart -f values.yaml
```

Or upgrade:

```bash
helm upgrade my-app ./ay-chart -f values.yaml
```

### Configuration

Refer to the [default `values.yaml`](./values.yaml) for a complete list of configurable options and examples.

You can override these values using a custom `values.yaml` file or with the `--set` flag during installation.

## Supported Values

| Key                                                         | Type   | Description                              | Default                        |
|-------------------------------------------------------------|--------|------------------------------------------|--------------------------------|
| `project.team`                                              | string | Team responsible for the service         | `sysops`                       |
| `project.url`                                               | string | Git URL or reference for the project     | `assertiveyield/...`           |
| `podSettings.dns.enabled`                                   | bool   | Enable custom DNS policy                 | `false`                        |
| `podSettings.dns.policy`                                    | string | DNS policy                               | `ClusterFirst`                 |
| `podSettings.dns.ndots`                                     | string | DNS `ndots` setting                      | `2`                            |
| `deployment.enabled`                                        | bool   | Enable the Deployment resource           | `true`                         |
| `deployment.replicas`                                       | int    | Replica count if autoscaling is disabled | `1`                            |
| `deployment.terminationGracePeriodSeconds`                  | int    | Grace period before forceful shutdown    | `60`                           |
| `deployment.containerPort`                                  | int    | Port the container exposes               | `80`                           |
| `deployment.clusterService`                                 | bool   | Enable ClusterIP service                 | `true`                         |
| `deployment.strategy.type`                                  | string | Deployment strategy type                 | `RollingUpdate`                |
| `deployment.strategy.maxUnavailable`                        | int    | Max unavailable pods during update       | `1`                            |
| `deployment.strategy.maxSurge`                              | int    | Max surge pods during update             | `1`                            |
| `deployment.autoscaling.enabled`                            | bool   | Enable Horizontal Pod Autoscaler         | `false`                        |
| `deployment.autoscaling.minReplicas`                        | int    | Minimum HPA replicas                     | `2`                            |
| `deployment.autoscaling.maxReplicas`                        | int    | Maximum HPA replicas                     | `10`                           |
| `deployment.autoscaling.targetCPU`                          | int    | CPU target for autoscaling (%)           | `80`                           |
| `deployment.autoscaling.targetMemory`                       | int    | Memory target for autoscaling (%)        | `80`                           |
| `deployment.ingress.enabled`                                | bool   | Enable ingress                           | `true`                         |
| `deployment.ingress.public`                                 | bool   | Flag for public vs internal ingress      | `false`                        |
| `deployment.ingress.hosts`                                  | list   | List of ingress hosts                    | `[sample.assertive.cc]`        |
| `deployment.ingress.tls`                                    | list   | TLS secrets for ingress                  | `[]`                           |
| `deployment.ingress.annotations`                            | object | Custom ingress annotations               | `haproxy.org/...`              |
| `deployment.containerSettings.image.repository`             | string | Docker image repository                  | `docker.ops.assertive.cc/...`  |
| `deployment.containerSettings.image.tag`                    | string | Docker image tag                         | `123123`                       |
| `deployment.containerSettings.image.pullPolicy`             | string | Image pull policy                        | `IfNotPresent`                 |
| `deployment.containerSettings.imagePullSecrets.*`           | object | Image pull secret(s)                     | `regcred`                      |
| `deployment.containerSettings.resources`                    | object | Resource requests and limits             | `{cpu: 100m, memory: 128Mi}`   |
| `deployment.containerSettings.env`                          | object | Environment variables                    | `{AY_ENVIRONMENT: Production}` |
| `deployment.containerSettings.prometheusMonitoring.enabled` | bool   | Enable Prometheus annotations            | `false`                        |
| `deployment.containerSettings.healthcheck.enabled`          | bool   | Enable health probes                     | `false`                        |
| `deployment.containerSettings.configMaps`                   | list   | List of mountable ConfigMaps             | `[]`                           |
| `nodeSettings.nodeRestrictions`                             | string | Pod scheduling restriction policy        | `none`                         |
| `nodeSettings.allowOtherAppsOnNode`                         | bool   | Allow co-scheduling on node              | `true`                         |
| `nodeSettings.instanceTypes`                                | list   | Instance type affinity                   | `[]`                           |
| `nodeSettings.nodeGroups`                                   | list   | Node group affinity                      | `[linux-amd64]`                |

## Testing

This chart includes a comprehensive test suite for validating all features including ConfigMaps, StatefulSets, persistent storage, and node scheduling.

For detailed testing documentation, examples, and ConfigMap testing specifics, see **[local/README.md](local/README.md)**.

```bash
# Quick start - run all tests with cleanup
./local/run-helm-tests.sh -test all -label -cleanup

# Run specific ConfigMap tests
./local/run-helm-tests.sh -test configmap-app,configmap-multiple,configmap-disabled
```

## Planned Features

| Feature                                   | Description                                                        | Effort | Impact | Done   |
|-------------------------------------------|--------------------------------------------------------------------|--------|--------|--------|
| Lifecycle hooks                           | Support `preStop` and `postStart` for graceful shutdown/init logic | Low    | Medium | No     |
| Tolerations and taints                    | Add tolerations for pod scheduling                                 | Low    | High   | No     |
| PodDisruptionBudgets                      | Add PDB support for HA during maintenance                          | Medium | High   | Review |
| NetworkPolicies                           | Add default-deny and optional rules for pod egress/ingress         | Medium | High   | No     |
| SecurityContext fine-tuning               | Support more granular container/pod security contexts              | Low    | High   | No     |
| Custom ServiceAccount                     | Allow using or creating serviceAccounts with specific permissions  | Medium | Medium | No     |
| Secrets mounting                          | Allow mounting Kubernetes Secrets like ConfigMaps                  | Low    | Medium | No     |
| PersistentVolumeClaim support             | Add support for applications that need storage                     | Medium | High   | ✅ Yes |
| ServiceMonitor support                    | Auto-create ServiceMonitor CR for Prometheus Operator              | Medium | High   | No     |
| Logging configuration                     | Inject logging config (fluent-bit, log levels)                     | Medium | Medium | No     |
| Stage-based overrides                     | Support `dev`, `staging`, `prod` profile layers                    | High   | Medium | No     |
| Git metadata injection                    | Inject commit SHA or build ID into labels/env                      | Medium | Medium | No     |
| Secrets/credentials helpers               | Generate passwords or integrate with External Secrets              | High   | High   | No     |
| ExternalDNS                               | Add annotations for global DNS automation                          | Medium | High   | No     |
| Mesh/gateway annotations (Istio, Cilium)  | Add support for sidecar injection, ingress gateway overrides       | Medium | High   | No     |
| Resource quotas                           | Add support for namespace-level resource quotas                    | Medium | High   | No     |
| Custom metrics support                    | Add support for custom metrics in HPA                              | Medium | High   | No     |
| Helm hooks support                        | Add pre-install, post-install hooks for custom logic               | Medium | Medium | No     |
| StatefulSet support                       | Add support for applications that require stable storage           | High   | High   | ✅ Yes |

## Generate Schema file

```
helm plugin install https://github.com/losisin/helm-values-schema-json.git
helm schema -f sample.yaml
```
#
