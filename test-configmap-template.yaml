# Test values for ConfigMap template validation
deployment:
  enabled: true
  replicas: 1
  containerPort: 80
  terminationGracePeriodSeconds: 30
  strategy:
    type: RollingUpdate
    maxSurge: 1
    maxUnavailable: 0
  autoscaling:
    enabled: false
  containerSettings:
    image:
      repository: "nginx"
      tag: "latest"
      pullPolicy: "IfNotPresent"
    imagePullSecrets:
      enabled: false
    resources:
      limits:
        cpu: 100m
        memory: 128Mi
      requests:
        cpu: 100m
        memory: 128Mi
    env:
      AY_ENVIRONMENT: Test
    prometheusMonitoring:
      enabled: false
    healthcheck:
      enabled: false
    persistence:
      enabled: false
    configMaps:
      - enabled: true
        name: app-config
        key: config.yaml
        mountPath: /etc/app/config.yaml
        content: |
          log_level: INFO
          retries: 3
      - enabled: false
        name: debug-config
        key: debug.yaml
        mountPath: /etc/app/debug.yaml
        content: |
          debug: true
          verbose: true
      - enabled: false
        name: unused-config
        key: unused.yaml
        mountPath: /etc/app/unused.yaml
        content: |
          should_not_render: true

statefulset:
  enabled: false

nodeSettings:
  nodeRestrictions: "none"
  allowOtherAppsOnNode: true
  instanceTypes: []
  nodeGroups:
    - bm
  arch:
    - amd64
  os:
    - linux
